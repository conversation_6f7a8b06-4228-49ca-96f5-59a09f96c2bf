const midtransClient = require('midtrans-client');

// Initialize Midtrans Snap client with configuration from environment variables
const snap = new midtransClient.Snap({
  isProduction: process.env.MIDTRANS_IS_PRODUCTION === 'true',
  serverKey: process.env.MIDTRANS_SERVER_KEY || '',
  clientKey: process.env.MIDTRANS_CLIENT_KEY || '',
});

// Initialize Midtrans Core API client for transaction status checking
const coreApi = new midtransClient.CoreApi({
  isProduction: process.env.MIDTRANS_IS_PRODUCTION === 'true',
  serverKey: process.env.MIDTRANS_SERVER_KEY || '',
  clientKey: process.env.MIDTRANS_CLIENT_KEY || '',
});

// Export Midtrans clients
export { snap, coreApi };

// Helper function to create a Midtrans transaction (equivalent to Xendit invoice)
export async function createTransaction({
  orderId,
  amount,
  description,
  customer,
  items,
  successRedirectUrl,
  failureRedirectUrl,
}: {
  orderId: string;
  amount: number;
  description: string;
  customer?: {
    email?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
  };
  items?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    category?: string;
  }>;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
}) {
  console.log('🚀 [MIDTRANS] Creating transaction:', {
    orderId,
    amount,
    description,
    customer: customer ? { ...customer, phone: customer.phone ? '***masked***' : undefined } : undefined,
    itemsCount: items?.length || 0,
  });

  try {
    // Prepare transaction parameter
    const parameter = {
      transaction_details: {
        order_id: orderId,
        gross_amount: amount,
      },
      item_details: items || [
        {
          id: 'subscription',
          name: description,
          price: amount,
          quantity: 1,
        },
      ],
      customer_details: customer,
      callbacks: {
        finish: successRedirectUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/success`,
        error: failureRedirectUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/error`,
        pending: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/pending`,
      },
      credit_card: {
        secure: true,
      },
    };

    console.log('📝 [MIDTRANS] Transaction parameter prepared:', {
      order_id: parameter.transaction_details.order_id,
      gross_amount: parameter.transaction_details.gross_amount,
      item_count: parameter.item_details.length,
      has_customer: !!parameter.customer_details,
    });

    // Create transaction using Snap API
    const transaction = await snap.createTransaction(parameter);

    console.log('✅ [MIDTRANS] Transaction created successfully:', {
      token: transaction.token ? 'token_generated' : 'no_token',
      redirect_url: transaction.redirect_url ? 'url_generated' : 'no_url',
    });

    return { 
      success: true, 
      data: {
        token: transaction.token,
        redirect_url: transaction.redirect_url,
        order_id: orderId,
      }
    };
  } catch (error) {
    console.error('❌ [MIDTRANS] Error creating transaction:', {
      orderId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      details: error,
    };
  }
}

// Helper function to get transaction status (equivalent to Xendit getInvoiceStatus)
export async function getTransactionStatus(orderId: string) {
  console.log('🔍 [MIDTRANS] Checking transaction status:', { orderId });

  try {
    const statusResponse = await coreApi.transaction.status(orderId);

    console.log('✅ [MIDTRANS] Transaction status retrieved:', {
      orderId,
      transaction_status: statusResponse.transaction_status,
      payment_type: statusResponse.payment_type,
      fraud_status: statusResponse.fraud_status,
      status_code: statusResponse.status_code,
    });

    return { 
      success: true, 
      data: statusResponse 
    };
  } catch (error) {
    console.error('❌ [MIDTRANS] Error getting transaction status:', {
      orderId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      details: error,
    };
  }
}

// Helper function to verify notification signature
export function verifyNotificationSignature(
  orderId: string,
  statusCode: string,
  grossAmount: string,
  signatureKey: string
): boolean {
  console.log('🔐 [MIDTRANS] Verifying notification signature:', {
    orderId,
    statusCode,
    grossAmount: '***masked***',
    signatureKey: signatureKey ? 'provided' : 'missing',
  });

  try {
    const crypto = require('crypto');
    const serverKey = process.env.MIDTRANS_SERVER_KEY || '';
    
    // Create signature string: order_id + status_code + gross_amount + server_key
    const signatureString = orderId + statusCode + grossAmount + serverKey;
    
    // Generate SHA512 hash
    const calculatedSignature = crypto
      .createHash('sha512')
      .update(signatureString)
      .digest('hex');

    const isValid = calculatedSignature === signatureKey;

    console.log('🔐 [MIDTRANS] Signature verification result:', {
      orderId,
      isValid,
      providedSignature: signatureKey ? 'provided' : 'missing',
      calculatedSignature: calculatedSignature ? 'calculated' : 'failed',
    });

    return isValid;
  } catch (error) {
    console.error('❌ [MIDTRANS] Error verifying signature:', {
      orderId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    
    return false;
  }
}

// Helper function to handle notification (equivalent to Xendit webhook processing)
export async function handleNotification(notificationBody: any) {
  console.log('📨 [MIDTRANS] Processing notification:', {
    order_id: notificationBody.order_id,
    transaction_status: notificationBody.transaction_status,
    payment_type: notificationBody.payment_type,
    fraud_status: notificationBody.fraud_status,
  });

  try {
    // Verify signature
    const isValidSignature = verifyNotificationSignature(
      notificationBody.order_id,
      notificationBody.status_code,
      notificationBody.gross_amount,
      notificationBody.signature_key
    );

    if (!isValidSignature) {
      console.error('❌ [MIDTRANS] Invalid notification signature:', {
        order_id: notificationBody.order_id,
      });
      
      return {
        success: false,
        error: 'Invalid signature',
      };
    }

    // Get latest transaction status from Midtrans API for verification
    const statusResult = await getTransactionStatus(notificationBody.order_id);
    
    if (!statusResult.success) {
      console.error('❌ [MIDTRANS] Failed to verify transaction status:', {
        order_id: notificationBody.order_id,
        error: statusResult.error,
      });
      
      return {
        success: false,
        error: 'Failed to verify transaction status',
      };
    }

    console.log('✅ [MIDTRANS] Notification processed successfully:', {
      order_id: notificationBody.order_id,
      verified_status: statusResult.data.transaction_status,
    });

    return {
      success: true,
      data: statusResult.data,
    };
  } catch (error) {
    console.error('❌ [MIDTRANS] Error processing notification:', {
      order_id: notificationBody.order_id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      details: error,
    };
  }
}

// Helper function to map Midtrans status to application status
export function mapMidtransStatus(transactionStatus: string, fraudStatus?: string): string {
  console.log('🔄 [MIDTRANS] Mapping transaction status:', {
    transactionStatus,
    fraudStatus,
  });

  let mappedStatus: string;

  switch (transactionStatus) {
    case 'capture':
      if (fraudStatus === 'accept') {
        mappedStatus = 'COMPLETED';
      } else if (fraudStatus === 'challenge') {
        mappedStatus = 'PENDING';
      } else {
        mappedStatus = 'FAILED';
      }
      break;
    case 'settlement':
      mappedStatus = 'COMPLETED';
      break;
    case 'pending':
      mappedStatus = 'PENDING';
      break;
    case 'deny':
    case 'cancel':
    case 'expire':
    case 'failure':
      mappedStatus = 'FAILED';
      break;
    default:
      mappedStatus = 'PENDING';
  }

  console.log('🔄 [MIDTRANS] Status mapped:', {
    from: transactionStatus,
    to: mappedStatus,
    fraudStatus,
  });

  return mappedStatus;
}
